"""
T2企业信贷分配多目标优化算法
使用NSGA-II算法求解期望收益最大化与风险最小化问题

作者：数学建模团队
日期：2025-01-08
"""

import pandas as pd
import numpy as np
import random
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.interpolate import interp1d
from deap import base, creator, tools, algorithms
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CreditAllocationOptimizer:
    """T2企业信贷分配多目标优化器"""
    
    def __init__(self):
        """初始化优化器"""
        print("🚀 初始化T2企业信贷分配优化器...")
        
        # 基础参数
        self.TOTAL_BUDGET = 100_000_000  # 1亿元
        self.MIN_RATE = 0.04  # 最低利率4%
        self.MAX_RATE = 0.15  # 最高利率15%
        self.MIN_LOAN = 100_000  # 最低贷款金额10万元
        self.MAX_LOAN = 1_000_000  # 最高贷款金额100万元
        
        # 算法参数
        self.POPULATION_SIZE = 100
        self.GENERATIONS = 300
        self.CROSSOVER_PROB = 0.8
        self.MUTATION_PROB = 0.1
        self.TOURNAMENT_SIZE = 3
        
        # 加载数据
        self.load_data()
        self.build_churn_interpolators()
        
        # 设置DEAP框架
        self.setup_deap()
        
        print(f"✅ 优化器初始化完成")
        print(f"   - 企业数量: {self.n_enterprises}家")
        print(f"   - 总预算: {self.TOTAL_BUDGET:,}元")
        print(f"   - 利率区间: {self.MIN_RATE:.1%}-{self.MAX_RATE:.1%}")
        print(f"   - 单企业贷款额度: {self.MIN_LOAN:,}-{self.MAX_LOAN:,}元")
        
    def load_data(self):
        """加载企业数据和流失率数据"""
        print("📂 加载数据文件...")
        
        # 加载企业数据
        self.enterprises = pd.read_csv('T2企业_优化算法输入.csv')
        self.n_enterprises = len(self.enterprises)
        
        # 加载流失率数据
        churn_data = pd.read_csv('4.csv')
        
        # 处理流失率数据格式
        self.churn_raw_data = {}
        rates = []
        for i, row in churn_data.iterrows():
            if i == 0:  # 跳过标题行
                continue
            if i == 1:  # 跳过评级标题行
                continue
            try:
                rate = float(row.iloc[0])
                rates.append(rate)
                self.churn_raw_data[rate] = {
                    'A': float(row.iloc[1]) if pd.notna(row.iloc[1]) else 0,
                    'B': float(row.iloc[2]) if pd.notna(row.iloc[2]) else 0, 
                    'C': float(row.iloc[3]) if pd.notna(row.iloc[3]) else 0
                }
            except (ValueError, IndexError):
                continue
                
        self.interest_rates = sorted(rates)
        print(f"   - 流失率数据点: {len(self.interest_rates)}个利率点")
        print(f"   - 利率范围: {min(self.interest_rates):.1%} - {max(self.interest_rates):.1%}")
        
        # 企业评级分布
        rating_dist = self.enterprises['继承信誉评级'].value_counts()
        print(f"   - 企业评级分布: A级{rating_dist.get('A', 0)}家, B级{rating_dist.get('B', 0)}家, C级{rating_dist.get('C', 0)}家")
        
    def build_churn_interpolators(self):
        """构建客户流失率插值函数"""
        print("🔧 构建客户流失率插值函数...")
        
        self.churn_interpolators = {}
        
        for rating in ['A', 'B', 'C']:
            rates = []
            churn_rates = []
            
            for rate in self.interest_rates:
                if rate in self.churn_raw_data:
                    rates.append(rate)
                    churn_rates.append(self.churn_raw_data[rate][rating])
            
            if len(rates) >= 2:
                # 创建插值函数，支持外推
                self.churn_interpolators[rating] = interp1d(
                    rates, churn_rates, 
                    kind='linear',
                    bounds_error=False, 
                    fill_value='extrapolate'
                )
                
                # 验证插值函数
                test_rate = 0.08
                churn_rate = self.get_churn_rate(rating, test_rate)
                print(f"   - {rating}级插值函数: 8%利率对应流失率{churn_rate:.3%}")
            else:
                print(f"   ⚠️ {rating}级数据不足，无法创建插值函数")
                
    def get_churn_rate(self, rating, interest_rate):
        """获取指定评级和利率下的客户流失率"""
        if rating not in self.churn_interpolators:
            return 0.5  # 默认流失率
            
        # 确保利率在合理范围内
        rate = max(self.MIN_RATE, min(self.MAX_RATE, interest_rate))
        churn = self.churn_interpolators[rating](rate)
        
        # 确保流失率在[0,1]范围内
        return max(0.0, min(1.0, churn))
    
    def setup_deap(self):
        """设置DEAP遗传算法框架"""
        print("⚙️ 设置NSGA-II算法框架...")
        
        # 清除之前的定义
        if hasattr(creator, "FitnessMulti"):
            del creator.FitnessMulti
        if hasattr(creator, "Individual"):
            del creator.Individual
            
        # 定义适应度函数：最大化收益，最小化风险
        creator.create("FitnessMulti", base.Fitness, weights=(1.0, -1.0))
        creator.create("Individual", list, fitness=creator.FitnessMulti)
        
        # 工具箱设置
        self.toolbox = base.Toolbox()
        
        # 基因生成函数
        self.toolbox.register("attr_loan_ratio", random.uniform, 0.0, 1.0)
        self.toolbox.register("attr_interest_rate", random.uniform, self.MIN_RATE, self.MAX_RATE)
        
        # 个体和种群生成
        self.toolbox.register("individual", self.create_individual)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # 遗传操作
        self.toolbox.register("evaluate", self.evaluate_individual)
        self.toolbox.register("mate", self.crossover)
        self.toolbox.register("mutate", self.mutate)
        self.toolbox.register("select", tools.selNSGA2)
        
    def create_individual(self):
        """创建个体（启发式初始化 + 随机初始化）"""
        individual = creator.Individual()
        
        # 决定使用启发式还是随机初始化
        if random.random() < 0.5:
            # 启发式初始化：根据企业评级分配资金
            individual = self.heuristic_initialization()
        else:
            # 随机初始化
            individual = self.random_initialization()
            
        return individual
    
    def heuristic_initialization(self):
        """启发式初始化：基于企业评级的倾斜性分配"""
        individual = creator.Individual()

        # 贷款金额比例（前n_enterprises个基因）
        loan_ratios = []
        rating_weights = {'A': 0.4, 'B': 0.3, 'C': 0.2}  # 评级权重

        # 估算可放贷企业数量（考虑单企业额度限制）
        max_enterprises = int(self.TOTAL_BUDGET / self.MIN_LOAN)
        target_enterprises = min(max_enterprises, int(self.n_enterprises * 0.6))  # 约60%企业获得贷款

        # 随机选择企业进行贷款
        selected_enterprises = random.sample(range(self.n_enterprises), target_enterprises)

        for i, (_, enterprise) in enumerate(self.enterprises.iterrows()):
            if i in selected_enterprises:
                rating = enterprise['继承信誉评级']
                base_weight = rating_weights.get(rating, 0.1)
                # 生成符合单企业额度约束的比例
                min_ratio = self.MIN_LOAN / self.TOTAL_BUDGET
                max_ratio = self.MAX_LOAN / self.TOTAL_BUDGET
                ratio = base_weight * random.uniform(0.5, 1.5) / target_enterprises
                ratio = max(min_ratio, min(max_ratio, ratio))
                loan_ratios.append(ratio)
            else:
                loan_ratios.append(0.0)

        # 归一化确保总和不超过1
        total_ratio = sum(loan_ratios)
        if total_ratio > 1.0:
            loan_ratios = [r / total_ratio for r in loan_ratios]

        individual.extend(loan_ratios)
        
        # 利率（后n_enterprises个基因）
        interest_rates = []
        rating_base_rates = {'A': 0.06, 'B': 0.09, 'C': 0.12}  # 基础利率
        
        for _, enterprise in self.enterprises.iterrows():
            rating = enterprise['继承信誉评级'] 
            base_rate = rating_base_rates.get(rating, 0.10)
            # 添加随机扰动
            rate = base_rate + random.uniform(-0.02, 0.02)
            rate = max(self.MIN_RATE, min(self.MAX_RATE, rate))
            interest_rates.append(rate)
            
        individual.extend(interest_rates)
        
        return individual
    
    def random_initialization(self):
        """随机初始化"""
        individual = creator.Individual()
        
        # 贷款金额比例
        for _ in range(self.n_enterprises):
            individual.append(random.uniform(0.0, 1.0))
            
        # 利率
        for _ in range(self.n_enterprises):
            individual.append(random.uniform(self.MIN_RATE, self.MAX_RATE))
            
        return individual
    
    def decode_individual(self, individual):
        """解码个体为贷款金额和利率"""
        # 分离基因
        loan_ratios = np.array(individual[:self.n_enterprises])
        interest_rates = np.array(individual[self.n_enterprises:])

        # 计算实际贷款金额
        total_ratio = np.sum(loan_ratios)
        if total_ratio > 0:
            # 归一化并乘以总预算
            normalized_ratios = loan_ratios / total_ratio
            loan_amounts = normalized_ratios * self.TOTAL_BUDGET
        else:
            loan_amounts = np.zeros(self.n_enterprises)

        # 应用单企业贷款额度约束
        for i in range(len(loan_amounts)):
            if loan_amounts[i] > 0:
                if loan_amounts[i] < self.MIN_LOAN:
                    loan_amounts[i] = 0  # 低于最小额度则不放贷
                elif loan_amounts[i] > self.MAX_LOAN:
                    loan_amounts[i] = self.MAX_LOAN  # 超过最大额度则限制

        # 重新归一化以满足预算约束
        total_loan = np.sum(loan_amounts)
        if total_loan > self.TOTAL_BUDGET:
            loan_amounts = loan_amounts * (self.TOTAL_BUDGET / total_loan)

        # 确保利率在合法范围内
        interest_rates = np.clip(interest_rates, self.MIN_RATE, self.MAX_RATE)

        return loan_amounts, interest_rates
    
    def evaluate_individual(self, individual):
        """评估个体的目标函数值"""
        loan_amounts, interest_rates = self.decode_individual(individual)
        
        # 检查预算约束
        total_loan = np.sum(loan_amounts)
        if total_loan > self.TOTAL_BUDGET * 1.01:  # 允许1%容忍度
            # 严重违反约束，给予惩罚
            return 0.0, self.TOTAL_BUDGET * 10
        
        total_return = 0.0  # 总收益
        total_risk = 0.0    # 总风险
        
        for i, (_, enterprise) in enumerate(self.enterprises.iterrows()):
            loan_amount = loan_amounts[i]
            interest_rate = interest_rates[i]

            # 检查单企业贷款额度约束
            if loan_amount >= self.MIN_LOAN and loan_amount <= self.MAX_LOAN:
                rating = enterprise['继承信誉评级']

                # 获取流失率
                churn_rate = self.get_churn_rate(rating, interest_rate)

                # 计算期望收益 = 贷款金额 × 利率 × (1 - 流失率)
                expected_return = loan_amount * interest_rate * (1 - churn_rate)
                total_return += expected_return

                # 计算风险损失 = 贷款金额 × 流失率
                risk_loss = loan_amount * churn_rate
                total_risk += risk_loss
        
        return total_return, total_risk
    
    def crossover(self, ind1, ind2):
        """双点交叉操作"""
        if random.random() < self.CROSSOVER_PROB:
            # 随机选择两个交叉点
            size = len(ind1)
            point1 = random.randint(1, size - 2)
            point2 = random.randint(point1 + 1, size - 1)
            
            # 交换中间段
            ind1[point1:point2], ind2[point1:point2] = ind2[point1:point2], ind1[point1:point2]
        
        return ind1, ind2
    
    def mutate(self, individual):
        """高斯变异操作（考虑单企业额度约束）"""
        for i in range(len(individual)):
            if random.random() < self.MUTATION_PROB:
                if i < self.n_enterprises:  # 贷款比例基因
                    individual[i] += random.gauss(0, 0.05)
                    individual[i] = max(0.0, min(1.0, individual[i]))
                else:  # 利率基因
                    individual[i] += random.gauss(0, 0.01)
                    individual[i] = max(self.MIN_RATE, min(self.MAX_RATE, individual[i]))

        # 确保变异后的个体满足单企业额度约束
        # 通过decode_individual方法自动处理约束
        loan_amounts, interest_rates = self.decode_individual(individual)

        # 重新编码回个体
        total_loan = np.sum(loan_amounts)
        if total_loan > 0:
            loan_ratios = loan_amounts / self.TOTAL_BUDGET
        else:
            loan_ratios = np.zeros(self.n_enterprises)

        # 更新个体
        individual[:self.n_enterprises] = loan_ratios.tolist()
        individual[self.n_enterprises:] = interest_rates.tolist()

        return individual,
    
    def run_optimization(self):
        """运行NSGA-II优化算法"""
        print(f"\n🔥 开始NSGA-II多目标优化...")
        print(f"   种群规模: {self.POPULATION_SIZE}")
        print(f"   进化代数: {self.GENERATIONS}")
        print(f"   交叉概率: {self.CROSSOVER_PROB}")
        print(f"   变异概率: {self.MUTATION_PROB}")
        
        # 统计信息记录
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean, axis=0)
        stats.register("std", np.std, axis=0)
        stats.register("min", np.min, axis=0)
        stats.register("max", np.max, axis=0)
        
        # 进度记录
        self.evolution_log = []
        
        # 初始化种群
        population = self.toolbox.population(n=self.POPULATION_SIZE)
        
        # 评估初始种群
        print("🔍 评估初始种群...")
        fitnesses = map(self.toolbox.evaluate, population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
        
        # 记录初代统计信息
        record = stats.compile(population)
        self.evolution_log.append(record)
        
        print(f"初代统计:")
        print(f"   平均收益: {record['avg'][0]:,.0f}元")
        print(f"   平均风险: {record['avg'][1]:,.0f}元")
        
        # 开始进化
        for generation in range(self.GENERATIONS):
            print(f"\r🚀 进化中... 第{generation+1}/{self.GENERATIONS}代", end="", flush=True)
            
            # 选择父代
            offspring = self.toolbox.select(population, self.POPULATION_SIZE)
            offspring = list(map(self.toolbox.clone, offspring))
            
            # 交叉和变异
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                self.toolbox.mate(child1, child2)
                del child1.fitness.values
                del child2.fitness.values
            
            for mutant in offspring:
                self.toolbox.mutate(mutant)
                del mutant.fitness.values
            
            # 评估无效个体
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            fitnesses = map(self.toolbox.evaluate, invalid_ind)
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit
            
            # 合并父代和子代，选择下一代
            population = self.toolbox.select(population + offspring, self.POPULATION_SIZE)
            
            # 记录统计信息
            record = stats.compile(population)
            self.evolution_log.append(record)
            
            # 每50代输出一次详细信息
            if (generation + 1) % 50 == 0:
                print(f"\n第{generation+1}代统计:")
                print(f"   平均收益: {record['avg'][0]:,.0f}元")
                print(f"   平均风险: {record['avg'][1]:,.0f}元")
                print(f"   最大收益: {record['max'][0]:,.0f}元")
                print(f"   最小风险: {record['min'][1]:,.0f}元")
        
        print(f"\n✅ 优化完成！")
        
        # 提取Pareto前沿
        pareto_front = tools.sortNondominated(population, len(population), first_front_only=True)[0]
        
        print(f"🎯 找到 {len(pareto_front)} 个Pareto最优解")
        
        return population, pareto_front, self.evolution_log
    
    def analyze_solutions(self, pareto_front):
        """分析Pareto最优解"""
        print(f"\n📊 Pareto最优解分析")
        print("="*80)
        
        solutions = []
        
        for i, solution in enumerate(pareto_front):
            loan_amounts, interest_rates = self.decode_individual(solution)
            total_return, total_risk = solution.fitness.values
            
            # 计算统计信息
            total_loan = np.sum(loan_amounts)
            avg_rate = np.average(interest_rates, weights=loan_amounts + 1e-6)
            active_loans = np.sum((loan_amounts >= self.MIN_LOAN) & (loan_amounts <= self.MAX_LOAN))
            
            # 各评级分配
            rating_allocation = {'A': 0, 'B': 0, 'C': 0}
            rating_count = {'A': 0, 'B': 0, 'C': 0}
            
            for j, (_, enterprise) in enumerate(self.enterprises.iterrows()):
                if loan_amounts[j] >= self.MIN_LOAN and loan_amounts[j] <= self.MAX_LOAN:
                    rating = enterprise['继承信誉评级']
                    rating_allocation[rating] += loan_amounts[j]
                    rating_count[rating] += 1
            
            solution_info = {
                'solution_id': i + 1,
                'total_return': total_return,
                'total_risk': total_risk,
                'total_loan': total_loan,
                'avg_rate': avg_rate,
                'active_loans': active_loans,
                'rating_allocation': rating_allocation,
                'rating_count': rating_count,
                'loan_amounts': loan_amounts,
                'interest_rates': interest_rates
            }
            
            solutions.append(solution_info)
            
            print(f"解{i+1:>2d}: 收益{total_return:>10,.0f}元 | 风险{total_risk:>10,.0f}元 | "
                  f"放贷{total_loan:>10,.0f}元 | 平均利率{avg_rate:.2%} | 放贷企业{active_loans:>3d}家")
        
        # 找出特征解
        solutions_df = pd.DataFrame([{
            '解ID': s['solution_id'],
            '总收益': s['total_return'],
            '总风险': s['total_risk'], 
            '总放贷': s['total_loan'],
            '平均利率': s['avg_rate'],
            '放贷企业数': s['active_loans']
        } for s in solutions])
        
        # 保守策略（风险最小）
        conservative_idx = solutions_df['总风险'].idxmin()
        conservative_solution = solutions[conservative_idx]
        
        # 激进策略（收益最大）  
        aggressive_idx = solutions_df['总收益'].idxmax()
        aggressive_solution = solutions[aggressive_idx]
        
        # 均衡策略（收益风险比最优）
        solutions_df['收益风险比'] = solutions_df['总收益'] / (solutions_df['总风险'] + 1)
        balanced_idx = solutions_df['收益风险比'].idxmax()
        balanced_solution = solutions[balanced_idx]
        
        print(f"\n🎯 推荐策略分析:")
        print(f"保守策略（解{conservative_solution['solution_id']}）: "
              f"收益{conservative_solution['total_return']:,.0f}元, "
              f"风险{conservative_solution['total_risk']:,.0f}元")
        
        print(f"均衡策略（解{balanced_solution['solution_id']}）: "
              f"收益{balanced_solution['total_return']:,.0f}元, "
              f"风险{balanced_solution['total_risk']:,.0f}元")
              
        print(f"激进策略（解{aggressive_solution['solution_id']}）: "
              f"收益{aggressive_solution['total_return']:,.0f}元, "
              f"风险{aggressive_solution['total_risk']:,.0f}元")
        
        return solutions, conservative_solution, balanced_solution, aggressive_solution
    
    def export_results(self, solutions, pareto_front):
        """导出优化结果"""
        print(f"\n💾 导出优化结果...")
        
        # 导出所有Pareto解的汇总
        summary_data = []
        for i, sol in enumerate(solutions):
            summary_data.append({
                '解ID': sol['solution_id'],
                '总收益(元)': sol['total_return'],
                '总风险(元)': sol['total_risk'],
                '总放贷额(元)': sol['total_loan'],
                '平均利率(%)': sol['avg_rate'] * 100,
                '放贷企业数': sol['active_loans'],
                'A级分配(元)': sol['rating_allocation']['A'],
                'B级分配(元)': sol['rating_allocation']['B'], 
                'C级分配(元)': sol['rating_allocation']['C'],
                'A级企业数': sol['rating_count']['A'],
                'B级企业数': sol['rating_count']['B'],
                'C级企业数': sol['rating_count']['C']
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv('T2_Pareto最优解汇总.csv', index=False, encoding='utf-8-sig')
        
        # 导出详细的分配方案（以均衡策略为例）
        if len(solutions) > 0:
            # 选择收益风险比最好的解作为推荐方案
            best_ratio = 0
            best_solution = solutions[0]
            
            for sol in solutions:
                ratio = sol['total_return'] / (sol['total_risk'] + 1)
                if ratio > best_ratio:
                    best_ratio = ratio
                    best_solution = sol
            
            # 创建详细分配表
            allocation_data = []
            for i, (_, enterprise) in enumerate(self.enterprises.iterrows()):
                loan_amount = best_solution['loan_amounts'][i]
                interest_rate = best_solution['interest_rates'][i]
                
                if loan_amount >= self.MIN_LOAN and loan_amount <= self.MAX_LOAN:
                    rating = enterprise['继承信誉评级']
                    churn_rate = self.get_churn_rate(rating, interest_rate)
                    expected_return = loan_amount * interest_rate * (1 - churn_rate)
                    risk_loss = loan_amount * churn_rate
                    
                    allocation_data.append({
                        '企业代号': enterprise['企业代号'],
                        '信誉评级': rating,
                        '贷款金额(元)': int(loan_amount),
                        '贷款利率(%)': round(interest_rate * 100, 4),
                        '预期流失率(%)': round(churn_rate * 100, 2),
                        '预期收益(元)': int(expected_return),
                        '风险损失(元)': int(risk_loss),
                        '相似度得分': enterprise['相似度得分']
                    })
            
            allocation_df = pd.DataFrame(allocation_data)
            allocation_df = allocation_df.sort_values(['信誉评级', '贷款金额(元)'], ascending=[True, False])
            allocation_df.to_csv('T2_推荐信贷分配方案.csv', index=False, encoding='utf-8-sig')
            
            print(f"✅ 结果文件已保存:")
            print(f"   - T2_Pareto最优解汇总.csv: {len(summary_df)}个最优解的汇总信息")
            print(f"   - T2_推荐信贷分配方案.csv: 推荐方案的详细分配表（{len(allocation_df)}家企业）")
            
            return allocation_df
        
        return None

def main():
    """主函数"""
    print("="*80)
    print("🏦 T2企业信贷分配多目标优化系统")
    print("   算法: NSGA-II")
    print("   目标: 期望收益最大化 + 风险最小化")
    print("   约束: 总预算≤1亿元, 利率∈[4%,15%], 单企业贷款∈[10万,100万]元")
    print("="*80)
    
    # 创建优化器
    optimizer = CreditAllocationOptimizer()
    
    # 运行优化
    population, pareto_front, evolution_log = optimizer.run_optimization()
    
    # 分析结果
    solutions, conservative, balanced, aggressive = optimizer.analyze_solutions(pareto_front)
    
    # 导出结果
    allocation_df = optimizer.export_results(solutions, pareto_front)
    
    print(f"\n🎉 T2企业信贷分配优化完成!")
    print(f"   找到 {len(pareto_front)} 个Pareto最优解")
    print(f"   推荐使用均衡策略：收益{balanced['total_return']:,.0f}元，风险{balanced['total_risk']:,.0f}元")
    print(f"   详细结果已保存到CSV文件中")
    
    return optimizer, solutions, allocation_df

if __name__ == "__main__":
    optimizer, solutions, allocation_df = main()
