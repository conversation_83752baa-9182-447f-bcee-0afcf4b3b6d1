#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
T2单目标优化：总利息收入最大化
验证将双目标合并为单目标的可行性
"""

import pandas as pd
import numpy as np
import random
from deap import base, creator, tools, algorithms
from scipy.interpolate import interp1d
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class SingleObjectiveT2Optimizer:
    def __init__(self):
        # 基础参数
        self.TOTAL_BUDGET = 100_000_000  # 1亿元
        self.MIN_RATE = 0.04  # 最低利率4%
        self.MAX_RATE = 0.15  # 最高利率15%
        self.MIN_LOAN = 100_000  # 最低贷款金额10万元
        self.MAX_LOAN = 1_000_000  # 最高贷款金额100万元
        
        # 算法参数
        self.POPULATION_SIZE = 100
        self.GENERATIONS = 200
        self.CROSSOVER_PROB = 0.8
        self.MUTATION_PROB = 0.1
        
        # 加载数据
        self.load_data()
        self.setup_deap()
        
        print(f"✅ 单目标优化器初始化完成")
        print(f"   - 企业数量: {self.n_enterprises}家")
        print(f"   - 总预算: {self.TOTAL_BUDGET:,}元") 
        print(f"   - 利率区间: {self.MIN_RATE:.1%}-{self.MAX_RATE:.1%}")
        print(f"   - 单企业贷款额度: {self.MIN_LOAN:,}-{self.MAX_LOAN:,}元")
        print(f"   - 目标: 总利息收入最大化")
    
    def load_data(self):
        """加载企业数据和流失率数据"""
        # 加载T2企业数据
        self.enterprises = pd.read_csv('T2/T2企业_最终完整数据.csv')
        
        # 排除D级企业
        self.enterprises = self.enterprises[self.enterprises['继承信誉评级'] != 'D']
        self.n_enterprises = len(self.enterprises)
        
        # 加载流失率数据
        churn_data = pd.read_csv('T2/4.csv')
        self.build_churn_interpolators(churn_data)
    
    def build_churn_interpolators(self, churn_data):
        """构建客户流失率插值函数"""
        self.churn_interpolators = {}
        
        # 解析数据
        rates = []
        churn_rates = {'A': [], 'B': [], 'C': []}
        
        for _, row in churn_data.iterrows():
            try:
                rate = float(row.iloc[0])
                rates.append(rate)
                churn_rates['A'].append(float(row.iloc[1]))
                churn_rates['B'].append(float(row.iloc[2]))
                churn_rates['C'].append(float(row.iloc[3]))
            except:
                continue
        
        # 创建插值函数
        for rating in ['A', 'B', 'C']:
            self.churn_interpolators[rating] = interp1d(
                rates, churn_rates[rating], 
                kind='linear',
                bounds_error=False, 
                fill_value='extrapolate'
            )
    
    def get_churn_rate(self, rating, interest_rate):
        """获取指定评级和利率下的客户流失率"""
        if rating in self.churn_interpolators:
            return max(0, min(1, self.churn_interpolators[rating](interest_rate)))
        return 0.5  # 默认流失率
    
    def setup_deap(self):
        """设置DEAP遗传算法框架"""
        # 创建适应度类和个体类
        creator.create("FitnessSingle", base.Fitness, weights=(1.0,))  # 单目标最大化
        creator.create("Individual", list, fitness=creator.FitnessSingle)
        
        # 创建工具箱
        self.toolbox = base.Toolbox()
        
        # 注册基因生成函数
        self.toolbox.register("loan_ratio", random.uniform, 0, 1)
        self.toolbox.register("interest_rate", random.uniform, self.MIN_RATE, self.MAX_RATE)
        
        # 注册个体和种群生成函数
        self.toolbox.register("individual", self.create_individual)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # 注册遗传操作
        self.toolbox.register("evaluate", self.evaluate_individual)
        self.toolbox.register("mate", self.crossover)
        self.toolbox.register("mutate", self.mutate)
        self.toolbox.register("select", tools.selTournament, tournsize=3)
    
    def create_individual(self):
        """创建个体"""
        individual = creator.Individual()
        
        # 前n_enterprises个基因：贷款金额比例
        for _ in range(self.n_enterprises):
            individual.append(self.toolbox.loan_ratio())
        
        # 后n_enterprises个基因：利率
        for _ in range(self.n_enterprises):
            individual.append(self.toolbox.interest_rate())
        
        return individual
    
    def decode_individual(self, individual):
        """解码个体为贷款金额和利率"""
        # 分离基因
        loan_ratios = np.array(individual[:self.n_enterprises])
        interest_rates = np.array(individual[self.n_enterprises:])
        
        # 计算实际贷款金额
        total_ratio = np.sum(loan_ratios)
        if total_ratio > 0:
            normalized_ratios = loan_ratios / total_ratio
            loan_amounts = normalized_ratios * self.TOTAL_BUDGET
        else:
            loan_amounts = np.zeros(self.n_enterprises)
            
        # 应用单企业贷款额度约束
        for i in range(len(loan_amounts)):
            if loan_amounts[i] > 0:
                if loan_amounts[i] < self.MIN_LOAN:
                    loan_amounts[i] = 0
                elif loan_amounts[i] > self.MAX_LOAN:
                    loan_amounts[i] = self.MAX_LOAN
        
        # 重新归一化以满足预算约束
        total_loan = np.sum(loan_amounts)
        if total_loan > self.TOTAL_BUDGET:
            loan_amounts = loan_amounts * (self.TOTAL_BUDGET / total_loan)
            
        # 确保利率在合法范围内
        interest_rates = np.clip(interest_rates, self.MIN_RATE, self.MAX_RATE)
        
        return loan_amounts, interest_rates
    
    def evaluate_individual(self, individual):
        """单目标评估函数：总利息收入最大化"""
        loan_amounts, interest_rates = self.decode_individual(individual)
        
        total_interest_income = 0.0
        
        for i in range(self.n_enterprises):
            loan_amount = loan_amounts[i]
            interest_rate = interest_rates[i]
            
            if loan_amount >= self.MIN_LOAN and loan_amount <= self.MAX_LOAN:
                # 计算利息收入 = 贷款金额 × 利率
                interest_income = loan_amount * interest_rate
                total_interest_income += interest_income
        
        return total_interest_income,
    
    def crossover(self, ind1, ind2):
        """双点交叉操作"""
        if random.random() < self.CROSSOVER_PROB:
            size = len(ind1)
            point1 = random.randint(1, size - 2)
            point2 = random.randint(point1 + 1, size - 1)
            
            # 交换中间段
            ind1[point1:point2], ind2[point1:point2] = ind2[point1:point2], ind1[point1:point2]
        
        return ind1, ind2
    
    def mutate(self, individual):
        """高斯变异操作"""
        for i in range(len(individual)):
            if random.random() < self.MUTATION_PROB:
                if i < self.n_enterprises:  # 贷款比例基因
                    individual[i] += random.gauss(0, 0.05)
                    individual[i] = max(0.0, min(1.0, individual[i]))
                else:  # 利率基因
                    individual[i] += random.gauss(0, 0.01)
                    individual[i] = max(self.MIN_RATE, min(self.MAX_RATE, individual[i]))
        
        return individual,
    
    def run_optimization(self):
        """运行单目标优化"""
        print(f"\n🎯 开始单目标优化（总利息收入最大化）...")
        
        # 初始化种群
        population = self.toolbox.population(n=self.POPULATION_SIZE)
        
        # 评估初始种群
        fitnesses = list(map(self.toolbox.evaluate, population))
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
        
        # 记录进化过程
        evolution_log = []
        
        # 进化循环
        for generation in range(self.GENERATIONS):
            # 选择
            offspring = self.toolbox.select(population, self.POPULATION_SIZE)
            offspring = list(map(self.toolbox.clone, offspring))
            
            # 交叉和变异
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                self.toolbox.mate(child1, child2)
                del child1.fitness.values
                del child2.fitness.values
            
            for mutant in offspring:
                self.toolbox.mutate(mutant)
                del mutant.fitness.values
            
            # 评估
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            fitnesses = map(self.toolbox.evaluate, invalid_ind)
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit
            
            # 更新种群
            population = offspring
            
            # 记录进化信息
            if generation % 50 == 0:
                best_fitness = max([ind.fitness.values[0] for ind in population])
                avg_fitness = np.mean([ind.fitness.values[0] for ind in population])
                print(f"   第{generation}代: 最佳={best_fitness:,.0f}元, 平均={avg_fitness:,.0f}元")
                evolution_log.append({
                    'generation': generation,
                    'best_fitness': best_fitness,
                    'avg_fitness': avg_fitness
                })
        
        # 找到最佳解
        best_individual = max(population, key=lambda x: x.fitness.values[0])
        best_fitness = best_individual.fitness.values[0]
        
        print(f"✅ 单目标优化完成！")
        print(f"   最佳总利息收入: {best_fitness:,.0f}元")
        
        return best_individual, best_fitness, evolution_log
    
    def analyze_solution(self, best_individual):
        """分析最优解"""
        loan_amounts, interest_rates = self.decode_individual(best_individual)
        
        print(f"\n📊 最优解分析:")
        
        # 基本统计
        total_loan = np.sum(loan_amounts)
        active_loans = np.sum(loan_amounts >= self.MIN_LOAN)
        avg_rate = np.average(interest_rates, weights=loan_amounts + 1e-6)
        
        print(f"   - 总放贷额: {total_loan:,.0f}元")
        print(f"   - 预算利用率: {total_loan/self.TOTAL_BUDGET:.1%}")
        print(f"   - 放贷企业数: {active_loans}家")
        print(f"   - 平均利率: {avg_rate:.2%}")
        
        # 按评级统计
        rating_stats = {'A': {'count': 0, 'amount': 0}, 
                       'B': {'count': 0, 'amount': 0}, 
                       'C': {'count': 0, 'amount': 0}}
        
        for i, (_, enterprise) in enumerate(self.enterprises.iterrows()):
            if loan_amounts[i] >= self.MIN_LOAN:
                rating = enterprise['继承信誉评级']
                rating_stats[rating]['count'] += 1
                rating_stats[rating]['amount'] += loan_amounts[i]
        
        print(f"\n   按信誉评级分布:")
        for rating in ['A', 'B', 'C']:
            count = rating_stats[rating]['count']
            amount = rating_stats[rating]['amount']
            print(f"     - {rating}级: {count}家, {amount:,.0f}元")
        
        return {
            'total_loan': total_loan,
            'active_loans': active_loans,
            'avg_rate': avg_rate,
            'rating_stats': rating_stats
        }

if __name__ == "__main__":
    print("🎯 T2单目标优化验证")
    print("   目标函数: 总利息收入最大化")
    print("   理论基础: 期望收益 + 利息损失 = 总利息收入")
    print("="*60)
    
    # 运行单目标优化
    optimizer = SingleObjectiveT2Optimizer()
    best_individual, best_fitness, evolution_log = optimizer.run_optimization()
    
    # 分析结果
    analysis = optimizer.analyze_solution(best_individual)
    
    print(f"\n🎉 验证完成！")
    print(f"   单目标优化成功实现了总利息收入最大化")
    print(f"   这证明了将双目标合并为单目标的可行性")
