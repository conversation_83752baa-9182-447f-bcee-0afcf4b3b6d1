import pandas as pd
import numpy as np
from deap import base, creator, tools, algorithms
import random
import warnings
warnings.filterwarnings('ignore')

class T3SimpleOptimizer:
    def __init__(self, t3_results):
        self.t3_results = t3_results
        self.n_enterprises = len(t3_results)
        self.budget = 1e8  # 1亿元预算
        
        # 基于AHP得分和信誉评级计算利率
        self.calculate_interest_rates()
        
    def calculate_interest_rates(self):
        """基于AHP得分和信誉评级计算利率"""
        # 基础利率设定
        base_rates = {'A': 4.5, 'B': 5.5, 'C': 6.5}  # 基础利率(%)
        
        rates = []
        risk_rates = []
        
        for _, enterprise in self.t3_results.iterrows():
            rating = enterprise.get('继承信誉评级', 'B')
            ahp_score = enterprise['T3_AHP综合得分']
            
            # 基础利率
            base_rate = base_rates.get(rating, 5.5)
            
            # 根据AHP得分调整利率（得分越高，利率越低）
            # AHP得分范围大约是0.3-0.7，映射到-1%到+1%的调整
            score_adjustment = (0.5 - ahp_score) * 2  # 得分高于0.5时减少利率
            
            final_rate = base_rate + score_adjustment
            final_rate = max(3.0, min(8.0, final_rate))  # 限制在3%-8%之间
            
            # 风险损失率（基于信誉评级）
            risk_loss_rates = {'A': 0.01, 'B': 0.03, 'C': 0.05}
            risk_rate = risk_loss_rates.get(rating, 0.03)
            
            rates.append(final_rate)
            risk_rates.append(risk_rate)
        
        self.t3_results['计算利率(%)'] = rates
        self.t3_results['风险损失率'] = risk_rates
        
        print(f"计算完成利率范围: {min(rates):.2f}% - {max(rates):.2f}%")
    
    def calculate_fitness(self, solution):
        """计算适应度函数"""
        total_allocation = np.sum(solution)
        
        if total_allocation == 0 or total_allocation > self.budget:
            return -1e10, 1e10  # 惩罚无效解
        
        # 计算收益
        revenue = 0
        risk = 0
        
        for i, allocation in enumerate(solution):
            if allocation > 0:
                enterprise = self.t3_results.iloc[i]
                interest_rate = enterprise['计算利率(%)'] / 100
                risk_loss_rate = enterprise['风险损失率']
                
                # 年收益 = 放贷金额 * 利率
                annual_revenue = allocation * interest_rate
                
                # 风险损失 = 放贷金额 * 风险损失率
                risk_loss = allocation * risk_loss_rate
                
                revenue += annual_revenue
                risk += risk_loss
        
        # 疫情风险调整
        pandemic_risk_factor = 1.2  # 疫情期间风险增加20%
        adjusted_risk = risk * pandemic_risk_factor
        
        return revenue, adjusted_risk

# 设置DEAP框架
creator.create("FitnessMax", base.Fitness, weights=(1.0, -1.0))  # 最大化收益，最小化风险
creator.create("Individual", list, fitness=creator.FitnessMax)

def generate_individual(n_enterprises, budget):
    """生成随机个体"""
    individual = [0.0] * n_enterprises
    
    # 随机选择要分配的企业数量(10-50个)
    n_selected = random.randint(10, min(50, n_enterprises))
    selected_indices = random.sample(range(n_enterprises), n_selected)
    
    # 随机分配预算
    remaining_budget = budget
    for i, idx in enumerate(selected_indices):
        if i == len(selected_indices) - 1:
            # 最后一个企业分配剩余预算
            individual[idx] = remaining_budget
        else:
            # 随机分配预算的一部分
            max_allocation = remaining_budget * 0.3  # 单个企业最多30%
            allocation = random.uniform(0, max_allocation)
            individual[idx] = allocation
            remaining_budget -= allocation
    
    return creator.Individual(individual)

def mutate_individual(individual, budget, indpb=0.1):
    """变异操作"""
    n = len(individual)
    
    for i in range(n):
        if random.random() < indpb:
            # 随机调整分配金额
            if individual[i] > 0:
                # 对已分配的企业进行调整
                adjustment = random.uniform(-0.2, 0.2) * individual[i]
                individual[i] = max(0, individual[i] + adjustment)
            else:
                # 可能给未分配的企业分配资金
                if random.random() < 0.1:  # 10%概率
                    individual[i] = random.uniform(0, budget * 0.05)
    
    # 确保不超预算
    total = sum(individual)
    if total > budget:
        factor = budget / total
        individual[:] = [x * factor for x in individual]
    
    return individual,

def crossover_individuals(ind1, ind2):
    """交叉操作"""
    n = len(ind1)
    
    # 单点交叉
    point = random.randint(1, n-1)
    ind1[point:], ind2[point:] = ind2[point:], ind1[point:]
    
    return ind1, ind2

# 主程序
if __name__ == "__main__":
    print("🚀 开始T3信贷分配简化优化...")
    
    # 读取数据
    t3_results = pd.read_csv('T3企业_AHP综合评估结果.csv')
    print(f"读取T3企业数据: {len(t3_results)}家企业")
    
    # 初始化优化器
    optimizer = T3SimpleOptimizer(t3_results)
    
    # 设置遗传算法参数
    toolbox = base.Toolbox()
    toolbox.register("individual", generate_individual, optimizer.n_enterprises, optimizer.budget)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", optimizer.calculate_fitness)
    toolbox.register("mate", crossover_individuals)
    toolbox.register("mutate", mutate_individual, optimizer.budget)
    toolbox.register("select", tools.selNSGA2)
    
    # 创建初始种群
    population = toolbox.population(n=100)
    
    # 评估初始种群
    fitnesses = toolbox.map(toolbox.evaluate, population)
    for ind, fit in zip(population, fitnesses):
        ind.fitness.values = fit
    
    print("开始进化优化...")
    # 进化参数
    NGEN = 50
    CXPB = 0.7
    MUTPB = 0.3
    
    # 统计信息
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean, axis=0)
    stats.register("min", np.min, axis=0)
    stats.register("max", np.max, axis=0)
    
    # 执行NSGA-II算法
    population, logbook = algorithms.eaNSGA2(
        population, toolbox, CXPB, MUTPB, NGEN,
        stats=stats, verbose=True
    )
    
    # 获取Pareto前沿
    pareto_solutions = tools.selNSGA2(population, len(population))
    
    print(f"\n✅ 优化完成！获得 {len(pareto_solutions)} 个解")
    
    # 分析结果
    solutions_analysis = []
    
    for i, solution in enumerate(pareto_solutions):
        allocation = solution
        fitness = solution.fitness.values
        
        total_allocation = np.sum(allocation)
        allocated_companies = np.sum(np.array(allocation) > 0)
        
        # 计算各级别分配
        a_allocation = sum(allocation[j] for j in range(len(allocation))
                          if allocation[j] > 0 and 
                          t3_results.iloc[j].get('继承信誉评级') == 'A')
        b_allocation = sum(allocation[j] for j in range(len(allocation))
                          if allocation[j] > 0 and 
                          t3_results.iloc[j].get('继承信誉评级') == 'B')
        c_allocation = sum(allocation[j] for j in range(len(allocation))
                          if allocation[j] > 0 and 
                          t3_results.iloc[j].get('继承信誉评级') == 'C')
        
        # 计算加权平均利率
        weighted_rate = 0
        if total_allocation > 0:
            weighted_rate = sum(allocation[j] * t3_results.iloc[j]['计算利率(%)']
                               for j in range(len(allocation)) if allocation[j] > 0) / total_allocation
        
        solutions_analysis.append({
            '解编号': i + 1,
            '总放贷额(万元)': total_allocation / 10000,
            '年收益(万元)': fitness[0] / 10000,
            '风险损失(万元)': fitness[1] / 10000,
            '放贷企业数': allocated_companies,
            'A级分配(万元)': a_allocation / 10000,
            'B级分配(万元)': b_allocation / 10000,
            'C级分配(万元)': c_allocation / 10000,
            '加权平均利率(%)': weighted_rate,
            '收益风险比': fitness[0] / max(fitness[1], 1)
        })
    
    # 保存结果
    results_df = pd.DataFrame(solutions_analysis)
    results_df = results_df.sort_values('收益风险比', ascending=False)
    results_df.to_csv('T3_简化优化结果.csv', index=False, encoding='utf-8-sig')
    
    print(f"✅ 优化结果已保存: T3_简化优化结果.csv")
    print("\n前5个最优解:")
    print(results_df.head().to_string(index=False))
    
    # 生成推荐方案
    best_idx = results_df['收益风险比'].idxmax()
    best_solution = pareto_solutions[best_idx]
    
    # 详细分配方案
    allocation_details = []
    for i, amount in enumerate(best_solution):
        if amount > 0:
            enterprise = t3_results.iloc[i]
            allocation_details.append({
                '企业代号': enterprise['企业代号'],
                'AHP排名': enterprise['T3_AHP排名'],
                'AHP得分': enterprise['T3_AHP综合得分'],
                '信誉评级': enterprise.get('继承信誉评级', 'N/A'),
                '分配金额(万元)': amount / 10000,
                '预期年利率(%)': enterprise['计算利率(%)'],
                '风险损失率': enterprise['风险损失率'],
                '年收益(万元)': (amount * enterprise['计算利率(%)'] / 100) / 10000
            })
    
    allocation_df = pd.DataFrame(allocation_details)
    allocation_df = allocation_df.sort_values('分配金额(万元)', ascending=False)
    allocation_df.to_csv('T3_推荐信贷分配方案.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 推荐分配方案已保存: T3_推荐信贷分配方案.csv")
    print(f"推荐方案摘要:")
    print(f"- 放贷企业数: {len(allocation_details)}")
    print(f"- 总放贷额: {sum(d['分配金额(万元)'] for d in allocation_details):.2f}万元")
    print(f"- 预期年收益: {sum(d['年收益(万元)'] for d in allocation_details):.2f}万元")
